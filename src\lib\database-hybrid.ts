// Hybrid database service - automatically switches between Supabase (online) and SQLite (offline)

import { supabaseAdmin } from './supabase';

// Dynamic imports for database implementations
let supabaseDb: any;
let sqliteDb: any;

// Network connectivity state
let isOnline = true;
let lastConnectivityCheck = 0;
const CONNECTIVITY_CHECK_INTERVAL = 30000; // 30 seconds

// Initialize database modules
const initDatabaseModules = () => {
  if (!supabaseDb) {
    supabaseDb = require('./database-supabase');
  }
  if (!sqliteDb) {
    sqliteDb = require('./database-sqlite-backup');
  }
};

// Check if Supabase is configured
const isSupabaseConfigured = () => {
  return !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
};

// Check network connectivity and Supabase availability
const checkConnectivity = async (): Promise<boolean> => {
  if (!isSupabaseConfigured()) {
    return false;
  }

  const now = Date.now();
  if (now - lastConnectivityCheck < CONNECTIVITY_CHECK_INTERVAL) {
    return isOnline;
  }

  try {
    // Quick connectivity test to Supabase
    const { error } = await supabaseAdmin
      .from('migrations')
      .select('count')
      .limit(1);
    
    isOnline = !error;
    lastConnectivityCheck = now;
    
    if (typeof window === 'undefined') {
      console.log(isOnline ? '🌐 Online - Using Supabase' : '📱 Offline - Using SQLite');
    }
    
    return isOnline;
  } catch (error) {
    isOnline = false;
    lastConnectivityCheck = now;
    
    if (typeof window === 'undefined') {
      console.log('📱 Offline - Using SQLite (Supabase unreachable)');
    }
    
    return false;
  }
};

// Get the appropriate database implementation
const getDatabaseImpl = async () => {
  initDatabaseModules();
  
  const online = await checkConnectivity();
  return online ? supabaseDb : sqliteDb;
};

// Hybrid database functions
export const initDatabase = async (): Promise<any> => {
  const db = await getDatabaseImpl();
  return db.initDatabase();
};

export const getDatabase = async (): Promise<any> => {
  const db = await getDatabaseImpl();
  return db.getDatabase();
};

export const closeDatabase = (): void => {
  // Close both databases to be safe
  if (supabaseDb) {
    supabaseDb.closeDatabase();
  }
  if (sqliteDb) {
    sqliteDb.closeDatabase();
  }
};

export const runMigrations = async (): Promise<void> => {
  const db = await getDatabaseImpl();
  return db.runMigrations();
};

// Sync function to synchronize offline changes to online database
export const syncOfflineToOnline = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  if (!isSupabaseConfigured()) {
    return { success: false, synced: 0, errors: ['Supabase not configured'] };
  }

  // Force connectivity check
  lastConnectivityCheck = 0;
  const online = await checkConnectivity();
  
  if (!online) {
    return { success: false, synced: 0, errors: ['Still offline'] };
  }

  // TODO: Implement actual sync logic based on your needs
  // This would involve:
  // 1. Reading pending changes from SQLite
  // 2. Applying them to Supabase
  // 3. Handling conflicts
  // 4. Marking changes as synced
  
  console.log('🔄 Sync functionality would be implemented here');
  
  return { success: true, synced: 0, errors: [] };
};

// Get current database status
export const getDatabaseStatus = async () => {
  const online = await checkConnectivity();
  
  return {
    isOnline: online,
    usingSupabase: online && isSupabaseConfigured(),
    usingSQLite: !online || !isSupabaseConfigured(),
    supabaseConfigured: isSupabaseConfigured(),
    lastCheck: new Date(lastConnectivityCheck).toISOString()
  };
};

// Force a specific database (for testing)
export const forceDatabase = (type: 'supabase' | 'sqlite') => {
  if (type === 'supabase') {
    isOnline = true;
  } else {
    isOnline = false;
  }
  lastConnectivityCheck = Date.now();
};

// Browser-side connectivity monitoring
if (typeof window !== 'undefined') {
  // Listen for online/offline events
  window.addEventListener('online', () => {
    console.log('🌐 Browser came online');
    lastConnectivityCheck = 0; // Force recheck
  });
  
  window.addEventListener('offline', () => {
    console.log('📱 Browser went offline');
    isOnline = false;
    lastConnectivityCheck = Date.now();
  });
}
