import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Client for browser/client-side operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Admin client for server-side operations (bypasses RLS)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: number
          username: string
          password_hash: string
          recovery_options: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          username: string
          password_hash: string
          recovery_options?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          username?: string
          password_hash?: string
          recovery_options?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      templates: {
        Row: {
          id: string
          name: string
          description: string | null
          filename: string
          placeholders: string | null
          layout_size: string
          has_applicant_photo: boolean
          uploaded_at: string
          updated_at: string
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          filename: string
          placeholders?: string | null
          layout_size?: string
          has_applicant_photo?: boolean
          uploaded_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          filename?: string
          placeholders?: string | null
          layout_size?: string
          has_applicant_photo?: boolean
          uploaded_at?: string
          updated_at?: string
        }
      }
      documents: {
        Row: {
          id: number
          template_id: string
          document_data: string | null
          generated_at: string
        }
        Insert: {
          id?: number
          template_id: string
          document_data?: string | null
          generated_at?: string
        }
        Update: {
          id?: number
          template_id?: string
          document_data?: string | null
          generated_at?: string
        }
      }
      migrations: {
        Row: {
          id: number
          name: string
          executed_at: string
        }
        Insert: {
          id?: number
          name: string
          executed_at?: string
        }
        Update: {
          id?: number
          name?: string
          executed_at?: string
        }
      }
    }
  }
}
