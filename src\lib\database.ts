// Database abstraction layer - hybrid offline/online support
// Automatically switches between SQLite (offline) and Supabase (online)

// For true offline/online hybrid support, use the hybrid implementation
// For simple environment-based switching, uncomment the lines below

// Option 1: Hybrid offline/online support (recommended)
export * from './database-hybrid';

// Option 2: Simple environment-based switching (uncomment to use)
/*
const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

let databaseModule: any;

if (useSupabase) {
  databaseModule = require('./database-supabase');
} else {
  databaseModule = require('./database-sqlite-backup');
}

export const initDatabase = databaseModule.initDatabase;
export const getDatabase = databaseModule.getDatabase;
export const closeDatabase = databaseModule.closeDatabase;
export const runMigrations = databaseModule.runMigrations;
*/
