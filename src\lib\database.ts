// Database abstraction layer - now using Supabase by default
// This file maintains backward compatibility during migration

// Check if we should use Supabase (default) or SQLite (fallback)
const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Log which database we're using
if (typeof window === 'undefined') {
  if (useSupabase) {
    console.log('🚀 Using Supabase database');
  } else {
    console.log('⚠️  Falling back to SQLite database - consider migrating to Supabase');
  }
}

// Dynamic imports and re-exports
let databaseModule: any;

if (useSupabase) {
  databaseModule = require('./database-supabase');
} else {
  databaseModule = require('./database-sqlite-backup');
}

// Re-export all functions from the chosen implementation
export const initDatabase = databaseModule.initDatabase;
export const getDatabase = databaseModule.getDatabase;
export const closeDatabase = databaseModule.closeDatabase;
export const runMigrations = databaseModule.runMigrations;
