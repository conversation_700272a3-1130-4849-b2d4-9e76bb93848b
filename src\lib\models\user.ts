// User model abstraction layer - conditionally uses Supabase or SQLite

// Check if we should use Supabase or SQLite
const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Dynamic imports and re-exports
let userModule: any;

if (useSupabase) {
  userModule = require('./user-supabase');
} else {
  userModule = require('./user-sqlite-backup');
}

// Re-export all classes and functions from the chosen implementation
export const UserModel = userModule.UserModel;
export const signup = userModule.signup;
export const login = userModule.login;
export const recoverAccount = userModule.recoverAccount;
export const hashPassword = userModule.hashPassword;
export const generatePrivateKey = userModule.generatePrivateKey;
export const encrypt = userModule.encrypt;
export const decrypt = userModule.decrypt;
export const SECURITY_QUESTIONS = userModule.SECURITY_QUESTIONS;

// Re-export types
export type User = userModule.User;
export type LoginCredentials = userModule.LoginCredentials;
export type SignupData = userModule.SignupData;
export type RecoveryData = userModule.RecoveryData;
