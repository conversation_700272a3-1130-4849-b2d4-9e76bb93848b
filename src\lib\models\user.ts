// User model abstraction layer - hybrid offline/online support
// Automatically switches between SQLite (offline) and Supabase (online)

// For true offline/online hybrid support, use the hybrid implementation
export * from './user-hybrid';

// For simple environment-based switching, uncomment the lines below
/*
const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

let userModule: any;

if (useSupabase) {
  userModule = require('./user-supabase');
} else {
  userModule = require('./user-sqlite-backup');
}

export const UserModel = userModule.UserModel;
export const signup = userModule.signup;
export const login = userModule.login;
export const recoverAccount = userModule.recoverAccount;
export const hashPassword = userModule.hashPassword;
export const generatePrivateKey = userModule.generatePrivateKey;
export const encrypt = userModule.encrypt;
export const decrypt = userModule.decrypt;
export const SECURITY_QUESTIONS = userModule.SECURITY_QUESTIONS;

export type User = userModule.User;
export type LoginCredentials = userModule.LoginCredentials;
export type SignupData = userModule.SignupData;
export type RecoveryData = userModule.RecoveryData;
*/
