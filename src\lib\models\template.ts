// Template model abstraction layer - hybrid offline/online support
// Automatically switches between SQLite (offline) and Supabase (online)

// For true offline/online hybrid support, use the hybrid implementation
export * from './template-hybrid';

// For simple environment-based switching, uncomment the lines below
/*
const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

let templateModule: any;

if (useSupabase) {
  templateModule = require('./template-supabase');
} else {
  templateModule = require('./template-sqlite-backup');
}

export const TemplateModel = templateModule.TemplateModel;
export const DocumentModel = templateModule.DocumentModel;
*/
