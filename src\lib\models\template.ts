// Template model abstraction layer - conditionally uses Supabase or SQLite

// Check if we should use Supabase or SQLite
const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Dynamic imports and re-exports
let templateModule: any;

if (useSupabase) {
  templateModule = require('./template-supabase');
} else {
  templateModule = require('./template-sqlite-backup');
}

// Re-export all classes and functions from the chosen implementation
export const TemplateModel = templateModule.TemplateModel;
export const DocumentModel = templateModule.DocumentModel;
